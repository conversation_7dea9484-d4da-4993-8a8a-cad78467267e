package models

import (
	"errors"
	"time"
)

// FinancialRecordResponse represents the API response model for financial records
type FinancialRecordResponse struct {
	ID              int        `json:"id"`
	OrderID         string     `json:"order_id"`
	TransactionID   string     `json:"transaction_id"`
	RevenueAmount   float64    `json:"revenue_amount"`
	TaxAmount       float64    `json:"tax_amount"`
	TotalAmount     float64    `json:"total_amount"`
	Currency        string     `json:"currency"`
	PaymentMethod   string     `json:"payment_method"`
	TransactionType string     `json:"transaction_type"`
	Description     string     `json:"description"`
	Status          string     `json:"status"`
	ProcessedAt     *time.Time `json:"processed_at,omitempty"`
	ProcessingNotes string     `json:"processing_notes,omitempty"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// FinancialRecordsListResponse represents paginated list response
type FinancialRecordsListResponse struct {
	Data       []FinancialRecordResponse `json:"data"`
	Pagination PaginationMetadata        `json:"pagination"`
}

// PaginationMetadata contains pagination information
type PaginationMetadata struct {
	Page        int  `json:"page"`
	Limit       int  `json:"limit"`
	Total       int  `json:"total"`
	TotalPages  int  `json:"total_pages"`
	HasNext     bool `json:"has_next"`
	HasPrevious bool `json:"has_previous"`
}

// StatusUpdateRequest represents the request model for status updates
type StatusUpdateRequest struct {
	Status string `json:"status" validate:"required,oneof=processed requires_attention completed"`
	Notes  string `json:"notes,omitempty"`
}

// StatusUpdateResponse represents the response model for status updates
type StatusUpdateResponse struct {
	ID              int       `json:"id"`
	Status          string    `json:"status"`
	ProcessedAt     time.Time `json:"processed_at"`
	ProcessingNotes string    `json:"processing_notes,omitempty"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ErrorResponse represents standardized error response
type ErrorResponse struct {
	Error   string            `json:"error"`
	Message string            `json:"message"`
	Code    int               `json:"code"`
	Details map[string]string `json:"details,omitempty"`
}

// AuditTrailEntry represents an audit trail record for status changes
type AuditTrailEntry struct {
	ID        int       `json:"id" db:"id"`
	RecordID  int       `json:"record_id" db:"record_id"`
	OldStatus *string   `json:"old_status" db:"old_status"`
	NewStatus string    `json:"new_status" db:"new_status"`
	Notes     *string   `json:"notes" db:"notes"`
	ChangedBy string    `json:"changed_by" db:"changed_by"`
	ChangedAt time.Time `json:"changed_at" db:"changed_at"`
	IPAddress *string   `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent *string   `json:"user_agent,omitempty" db:"user_agent"`
}

// NewAuditTrailEntry creates a new audit trail entry
func NewAuditTrailEntry(recordID int, oldStatus *string, newStatus string, notes *string, changedBy string, ipAddress *string, userAgent *string) *AuditTrailEntry {
	return &AuditTrailEntry{
		RecordID:  recordID,
		OldStatus: oldStatus,
		NewStatus: newStatus,
		Notes:     notes,
		ChangedBy: changedBy,
		ChangedAt: time.Now(),
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}
}

// Validate validates the audit trail entry
func (a *AuditTrailEntry) Validate() error {
	if a.RecordID <= 0 {
		return &ValidationError{Field: "record_id", Message: "record_id must be greater than 0"}
	}

	if a.NewStatus == "" {
		return &ValidationError{Field: "new_status", Message: "new_status is required"}
	}

	if a.ChangedBy == "" {
		return &ValidationError{Field: "changed_by", Message: "changed_by is required"}
	}

	return nil
}

// AuthContext represents authentication context for requests
type AuthContext struct {
	APIKey      string    `json:"api_key,omitempty"`
	UserID      string    `json:"user_id,omitempty"`
	ClientID    string    `json:"client_id,omitempty"`
	Permissions []string  `json:"permissions,omitempty"`
	ExpiresAt   time.Time `json:"expires_at,omitempty"`
	IPAddress   string    `json:"ip_address,omitempty"`
	UserAgent   string    `json:"user_agent,omitempty"`
}

// APIKeyInfo represents API key information
type APIKeyInfo struct {
	ID          string     `json:"id" db:"id"`
	Key         string     `json:"key" db:"key"`
	Name        string     `json:"name" db:"name"`
	ClientID    string     `json:"client_id" db:"client_id"`
	Permissions []string   `json:"permissions" db:"permissions"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty" db:"expires_at"`
	LastUsedAt  *time.Time `json:"last_used_at,omitempty" db:"last_used_at"`
}

// RateLimitInfo represents rate limiting information
type RateLimitInfo struct {
	Key          string    `json:"key"`
	RequestCount int       `json:"request_count"`
	WindowStart  time.Time `json:"window_start"`
	WindowSize   int       `json:"window_size_seconds"`
	MaxRequests  int       `json:"max_requests"`
	ResetTime    time.Time `json:"reset_time"`
}

// QueryFilters represents the query parameters for financial records
type QueryFilters struct {
	DateFrom       *time.Time `json:"date_from,omitempty"`
	DateTo         *time.Time `json:"date_to,omitempty"`
	OrderReference string     `json:"order_reference,omitempty"`
	Status         string     `json:"status,omitempty"`
	Page           int        `json:"page"`
	Limit          int        `json:"limit"`
}

// Validate validates the query filters
func (qf *QueryFilters) Validate() error {
	if qf.Page < 1 {
		qf.Page = 1
	}
	if qf.Limit < 1 || qf.Limit > 100 {
		qf.Limit = 20 // Default limit
	}
	if qf.DateFrom != nil && qf.DateTo != nil && qf.DateFrom.After(*qf.DateTo) {
		return errors.New("date_from cannot be after date_to")
	}
	if qf.Status != "" {
		validStatuses := map[string]bool{
			"pending":            true,
			"processed":          true,
			"requires_attention": true,
			"completed":          true,
		}
		if !validStatuses[qf.Status] {
			return errors.New("invalid status value")
		}
	}

	return nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return e.Message
}

// ToFinancialRecordResponse converts FinancialEntry to API response model
func (fe *FinancialEntry) ToFinancialRecordResponse() FinancialRecordResponse {
	return FinancialRecordResponse{
		ID:              fe.ID,
		OrderID:         fe.OrderID,
		TransactionID:   fe.TransactionID,
		RevenueAmount:   fe.RevenueAmount,
		TaxAmount:       fe.TaxAmount,
		TotalAmount:     fe.CalculateTotalAmount(),
		Currency:        fe.Currency,
		PaymentMethod:   fe.PaymentMethod,
		TransactionType: fe.TransactionType,
		Description:     fe.Description,
		Status:          "pending", // Default status for existing records
		ProcessedAt:     nil,
		ProcessingNotes: "",
		CreatedAt:       fe.CreatedAt,
		UpdatedAt:       fe.UpdatedAt,
	}
}
