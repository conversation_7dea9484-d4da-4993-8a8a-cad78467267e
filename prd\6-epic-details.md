# 6. Epic Details

### Epic 1: Platform Foundation & Core Service Framework
**Epic Goal**: The goal of this epic is to build a solid, automated, and secure development and deployment foundation for the entire CDH platform. By the end of this epic, we will have a functional CI/CD pipeline, a configured API gateway, and a core user service capable of handling user registration and authentication. This will prove the viability of our technical architecture and pave the way for subsequent business feature development.

#### User Stories
**Story 1.1: Project Initialization & Repository Setup**
* **As a** developer, **I want** a standardized project structure using a Monorepo pattern, **so that** I can manage the code and dependencies for all microservices in a unified environment.
* **Acceptance Criteria**:
    1.  The Git repository has been created.
    2.  The repository contains a Monorepo directory structure (e.g., `apps/` and `packages/` folders) that aligns with our technology choices (Go, Kafka, Traefik).
    3.  A `README.md` file has been created in the root directory with a project overview.

**Story 1.2: Basic CI/CD Automated Deployment Pipeline**
* **As a** platform team member, **I want** a basic continuous integration/continuous deployment (CI/CD) pipeline, **so that** a "Hello World" application can be automatically built, tested, and deployed to the Kubernetes cluster after a code commit.
* **Acceptance Criteria**:
    1.  A CI/CD configuration file (e.g., `.github/workflows/deploy.yml`) has been created.
    2.  The pipeline is automatically triggered when code is pushed to the `main` branch.
    3.  The pipeline successfully deploys a simple application to K8s, which is accessible via a URL.

**Story 1.3: API Gateway (Traefik) Initial Deployment & Routing**
* **As a** platform team member, **I want** to deploy and configure Traefik in the Kubernetes cluster, **so that** it can act as the single entry point for all internal services and handle basic HTTP routing.
* **Acceptance Criteria**:
    1.  Traefik has been successfully deployed to the K8s cluster.
    2.  The Traefik dashboard is accessible.
    3.  A basic routing rule pointing to a "health check" endpoint is configured and working correctly.

**Story 1.4: User & Permissions Service Scaffolding**
* **As a** developer, **I want** to create the basic code structure for the User & Permissions service and implement a health check endpoint, **so that** we can deploy it via the CI/CD pipeline and verify that the service is accessible through the API Gateway.
* **Acceptance Criteria**:
    1.  A Go project structure has been created in the `apps/user-service` directory.
    2.  The service includes a `/health` endpoint that returns `{"status": "ok"}` when accessed.
    3.  A Dockerfile for the service has been created and can successfully build a Docker image.
    4.  After deployment, the service is successfully accessible via a path through the API Gateway (e.g., `https://api.yourdomain.com/user/health`).

**Story 1.5: Implement User Registration**
* **As a** future system developer, **I want** to call a user registration API endpoint, **so that** I can create a new user for future frontend applications or systems.
* **Acceptance Criteria**:
    1.  The User service provides a `POST /register` endpoint.
    2.  The endpoint accepts user information (e.g., username, password, email) and stores the user data with an encrypted password in the PostgreSQL database.
    3.  Upon successful user creation, the new user's ID is returned.
    4.  If the username or email already exists, an appropriate error message is returned.

**Story 1.6: Implement User Authentication (JWT)**
* **As a** future system user, **I want** to authenticate with a username and password to obtain an access token, **so that** I can use this token to access protected API resources.
* **Acceptance Criteria**:
    1.  The User service provides a `POST /login` endpoint.
    2.  The endpoint validates user credentials.
    3.  Upon successful validation, a JWT (JSON Web Token) containing the user ID and role is generated and returned to the client.
    4.  If credentials are incorrect, an authentication failure error message is returned.

**Story 1.7: Implement Basic Authentication Middleware**
* **As a** developer, **I want** an authentication middleware that can be integrated into the API Gateway or microservices, **so that** I can protect specific API endpoints, allowing access only to requests with a valid JWT.
* **Acceptance Criteria**:
    1.  The authentication middleware has been created.
    2.  When a protected endpoint is called, the middleware validates the JWT in the request header.
    3.  If the JWT is valid, the request is passed through to the target service.
    4.  If the JWT is invalid or missing, a 401 Unauthorized error is returned.

### Epic 2: Core Transactional Flow Implementation
**Epic Goal**: The goal of this epic is to build the CDH platform's capability to handle core business transactions. By the end of this epic, the system will be able to receive orders via an API, the order information will be reliably transmitted via Kafka to the inventory service, and inventory levels will be updated accurately and in real-time. This will validate the core pattern of our event-driven architecture and establish the data foundation for the subsequent financial integration.

#### User Stories
**Story 2.1: Order Service Scaffolding & API Definition**
* **As a** developer, **I want** to create the basic code structure for the Order service and define the API interfaces for creating and querying orders, **so that** future systems can begin interacting with the Order service.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/order-service` directory.
    2.  The service provides a `POST /orders` endpoint for creating orders and a `GET /orders/{id}` endpoint for querying order details.
    3.  The order data model (including order number, product info, quantity, price, etc.) has been defined in the code.
    4.  The service is connected to its independent PostgreSQL database.

**Story 2.2: Implement Order Creation & Event Publishing**
* **As a** future system developer, **I want** the system to publish an "Order Created" event after successfully creating an order via the `POST /orders` endpoint, **so that** other downstream services interested in this event (like the Inventory service) can receive and process it.
* **Acceptance Criteria**:
    1.  When `POST /orders` successfully processes a request, the order data is correctly stored in the Order service's database.
    2.  After the order is successfully stored, an event containing the complete order information is serialized (e.g., in JSON format) and published to the `orders.created` topic in Kafka.
    3.  Upon successful API call, the created order information and order ID are returned.

**Story 2.3: Inventory Service Scaffolding & Kafka Consumer**
* **As a** developer, **I want** to create the basic code structure for the Inventory service and implement a Kafka consumer to listen for order events, **so that** the Inventory service can receive and prepare to process messages from the Order service.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/inventory-service` directory.
    2.  The service is connected to its independent PostgreSQL database, and the inventory data model (e.g., SKU, stock quantity, reserved quantity) has been defined.
    3.  A Kafka consumer has been implemented in the service, subscribing to the `orders.created` topic.
    4.  When a new message arrives, the service can successfully receive it and print its content to the logs.

**Story 2.4: Implement Inventory Deduction based on Order Events**
* **As an** inventory manager, **I want** the system to automatically deduct the corresponding quantity from the total stock after each successful sale, **so that** the inventory levels seen across all channels are accurate and in real-time.
* **Acceptance Criteria**:
    1.  The Inventory service's Kafka consumer can successfully parse the product SKUs and quantities from the "Order Created" event.
    2.  The service finds the corresponding products in the inventory database and deducts the stock quantity based on the parsed information.
    3.  The inventory update operation is atomic to ensure data consistency.
    4.  If a product is out of stock, a failure event should be logged or sent to a specific Kafka topic for subsequent processing.

**Story 2.5: Provide Inventory Query API**
* **As a** future POS or e-commerce system developer, **I want** to call an API to query the current stock level of one or more products, **so that** I can display accurate inventory information to the user before they place an order, preventing overselling.
* **Acceptance Criteria**:
    1.  The Inventory service provides a `GET /inventory?sku={sku1},{sku2}` API endpoint.
    2.  The endpoint can return the real-time stock quantity for the specified product SKUs.
    3.  The API response is fast, using Redis as a query cache for optimization.

### Epic 3: Financial Integration & E-Invoicing Compliance
**Epic Goal**: The goal of this epic is to complete the most critical compliance feature of the CDH platform—the fully automated integration with the LHDN MyInvois system. By the end of this epic, the platform will be able to listen for completed order events, automatically generate financial records, and call the official LHDN API to handle the generation, submission, and validation of e-invoices. This will ensure the company's operations are fully compliant with the latest Malaysian tax regulations while significantly improving financial processing efficiency.

#### User Stories
**Story 3.1: Finance Service Scaffolding & Event Listening**
* **As a** developer, **I want** to create the basic code structure for the Finance service and enable it to consume order events from Kafka like the Inventory service, **so that** the Finance service can obtain all the raw order data needed to generate financial records and e-invoices.
* **Acceptance Criteria**:
    1.  A Go project structure and its Dockerfile have been created in the `apps/finance-service` directory.
    2.  The service is connected to its independent PostgreSQL database, and data models for financial entries and e-invoices have been defined.
    3.  A Kafka consumer has been implemented in the service, subscribing to the `orders.created` topic.
    4.  When a new message arrives, the service can successfully receive it and store its content in a temporary processing table.

**Story 3.2: Implement Basic Automated Financial Entry**
* **As a** finance staff member, **I want** the system to automatically generate corresponding accounting entries (e.g., debit accounts receivable, credit sales revenue) after each order is completed, **so that** manual bookkeeping workload is reduced and accuracy is ensured.
* **Acceptance Criteria**:
    1.  After processing an "Order Created" event, the Finance service can automatically generate accounting-compliant financial entries based on order amount, taxes, etc.
    2.  The generated financial entries are correctly stored in the finance database.
    3.  The system can handle orders with different payment methods and tax-inclusive/exclusive pricing.

**Story 3.3: Integrate with LHDN MyInvois API**
* **As a** system, **I want** to securely connect to the LHDN MyInvois Sandbox environment and complete authentication, **so that** I am prepared for the subsequent e-invoice submission and validation functionalities.
* **Acceptance Criteria**:
    1.  An LHDN API client module has been implemented in the Finance service.
    2.  The system can successfully initiate an authentication request to the LHDN MyInvois Sandbox environment using secure credentials (e.g., API Key, OAuth Token).
    3.  The access token obtained after successful authentication is properly managed and refreshed.
    4.  All communication with LHDN must be encrypted via HTTPS.

**Story 3.4: Implement Automated E-Invoice Generation & Submission**
* **As a** compliance officer, **I want** the system to automatically generate LHDN-compliant e-invoices based on confirmed order information and submit them to the MyInvois system, **so that** I can ensure every transaction completes the tax compliance procedure in a timely and accurate manner.
* **Acceptance Criteria**:
    1.  The Finance service can transform internal order data into the specified format required by the LHDN MyInvois API (e.g., JSON or XML).
    2.  The transformed data is successfully submitted to the LHDN MyInvois Sandbox system via the API client.
    3.  Upon successful submission, the system receives a unique invoice identifier from the LHDN API (e.g., Invoice ID, IRBM Unique ID).
    4.  This unique identifier and the invoice's submission status (e.g., `Submitted`) are stored in our finance database and associated with the original order.

**Story 3.5: Implement E-Invoice Status Query & Update**
* **As a** finance staff member, **I want** the system to be able to periodically or manually query the latest status of submitted e-invoices (e.g., `Validated`, `Rejected`), **so that** I can promptly address rejected invoices or confirm that all invoices have been successfully validated.
* **Acceptance Criteria**:
    1.  The Finance service provides an internal interface or a scheduled task to call the LHDN API to query the status of a specific e-invoice.
    2.  The queried latest status (`Validated`, `Rejected`, etc.) is updated in our database.
    3.  If an invoice is `Rejected`, the system should record the reason for rejection and create a pending task for the finance staff to follow up.

### Epic 4: Platform Observability & Management
**Epic Goal**: The goal of this epic is to establish comprehensive "observability" capabilities for the CDH platform and provide basic management functionalities. By the end of this epic, we will have a centralized logging system to track issues, a real-time monitoring dashboard to observe system health, and a simple internal admin backend to manage users and view key configurations. This will ensure our operations team can easily monitor, maintain, and manage the entire platform, guaranteeing its long-term stability.

#### User Stories
**Story 4.1: Deploy Centralized Logging System (EFK Stack)**
* **As an** operations engineer, **I want** a system that centrally collects, stores, and queries the logs of all microservices (User, Order, Inventory, Finance), **so that** I can quickly track, locate, and diagnose failures when issues occur.
* **Acceptance Criteria**:
    1.  Elasticsearch, Fluentd, and Kibana (EFK) have been successfully deployed to the Kubernetes cluster.
    2.  All microservice logs (stdout/stderr) are automatically collected by Fluentd and sent to Elasticsearch.
    3.  Developers can query all logs via the Kibana web interface based on criteria like service name, time, and keywords.

**Story 4.2: Deploy Monitoring & Alerting System (Prometheus & Grafana)**
* **As an** operations engineer, **I want** a system that monitors key performance indicators (KPIs) of all microservices in real-time, **so that** I can intuitively understand the system's health and receive alerts before problems occur.
* **Acceptance Criteria**:
    1.  Prometheus and Grafana have been successfully deployed to the Kubernetes cluster.
    2.  Each Go microservice exposes a `/metrics` endpoint providing key metrics (e.g., API request latency, error rate, memory usage).
    3.  Prometheus can automatically scrape these metrics.
    4.  At least one basic dashboard has been created in Grafana, displaying the core health metrics of all services.
    5.  Basic alert rules have been set for critical metrics (e.g., API error rate > 5%).

**Story 4.3: Create Internal Admin Backend Framework**
* **As a** system administrator, **I want** a basic, password-protected web application to serve as the internal admin backend, **so that** I have a unified interface to perform administrative tasks.
* **Acceptance Criteria**:
    1.  A simple frontend application project (e.g., using React or Vue) has been created.
    2.  The application includes a login page, allowing administrators to log in using an account with an "admin" role created in the User service.
    3.  The basic layout of the application (e.g., side navigation bar, main content area) has been implemented.

**Story 4.4: Implement User Management in Admin Backend**
* **As a** system administrator, **I want** to view a list of all users in the admin backend and be able to create new users and assign roles, **so that** I can conveniently manage platform users and permissions without directly operating the database.
* **Acceptance Criteria**:
    1.  There is a "User Management" page in the admin backend.
    2.  The page displays a list of all registered users by calling the User service's API.
    3.  The page includes a form that can be used to create new users and assign them roles (e.g., "Admin," "System Integration Account").

